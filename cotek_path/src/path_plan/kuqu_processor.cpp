#include "path_plan/kuqu_processor.hpp"
#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include <algorithm>
#include <limits>
#include <cmath>

// 生成唯一ID的函数
extern std::string generateUUID();

// 构造函数
KuquProcessor::KuquProcessor(GraphBuilder& graph_builder, std::vector<cotek::task_t>& all_tasks) 
    : graph_builder(graph_builder), all_tasks(all_tasks) {
}

// 主要处理方法
void KuquProcessor::processKuquTasks(const std::vector<cotek::task_t>& all_tasks) {
    // 清空之前的数据
    clear();
    
    // 分离库区任务和正常任务
    // separateKuquTasks(all_tasks);
    
    // 更新GraphBuilder的正常任务列表
    all_normal_tasks = normal_tasks;
    
    // 处理库区绑定（在拓扑图构建完成后调用）
    bindAllKuquToTopology();
}

// 分离库区任务和正常任务
void KuquProcessor::separateKuquTasks(const std::vector<cotek::task_t>& tasks) {
    kuqu_tasks.clear();
    normal_tasks.clear();
    
    for (const auto& task : tasks) {
        if (task.kuqu_id != -1) {
            // 库区任务
            kuqu_tasks.push_back(task);
            LOG_INFO_STREAM("Found kuqu task: " << task.id << " with kuqu_id: " << task.kuqu_id);
            
            // 初始化库区边映射
            std::vector<CutKuquEdge> edges;
            for (const auto& edge : task.edges) {
                CutKuquEdge kuqu_edge;
                kuqu_edge.edge_id = edge.id;
                kuqu_edge.task_id = task.id;
                kuqu_edge.original_points = edge.points;
                kuqu_edge.cut_points = edge.points; // 初始时切割后的点集等于原始点集
                kuqu_edge.cut_index = 0;
                
                // 判断边的方向：根据点的type字段判断是否为正向边
                // 假设正向边的type >= 0，反向边的type < 0
                bool has_positive = false, has_negative = false;
                for (const auto& point : edge.points) {
                    if (point.type >= 0) has_positive = true;
                    if (point.type < 0) has_negative = true;
                }
                kuqu_edge.is_forward = has_positive && !has_negative;
                
                edges.push_back(kuqu_edge);
            }
            kuqu_edges_map[std::to_string(task.kuqu_id)] = edges;
        } else {
            // 正常任务
            normal_tasks.push_back(task);
        }
    }
    
    LOG_INFO_STREAM("Separated tasks: " << kuqu_tasks.size() << " kuqu tasks, " 
                    << normal_tasks.size() << " normal tasks");
}

// 处理库区任务完成后的总结信息
void KuquProcessor::logKuquBindingSummary() {
    LOG_INFO_STREAM("=== Kuqu Binding Summary ===");
    for (const auto& [kuqu_id, bindings] : kuqu_bindings) {
        LOG_INFO_STREAM("Kuqu " << kuqu_id << " has " << bindings.size() << " binding(s):");
        for (size_t i = 0; i < bindings.size(); ++i) {
            const auto& binding = bindings[i];
            LOG_INFO_STREAM("  Binding " << (i+1) << ": topology edge " << binding.topology_edge_id 
                           << ", end node " << binding.end_node_id 
                           << ", cut points " << binding.cut_points.size());
        }
    }
    LOG_INFO_STREAM("=== End Kuqu Binding Summary ===");
}

// 处理所有库区任务的绑定
void KuquProcessor::bindAllKuquToTopology() {
    if (kuqu_tasks.empty()) {
        return;
    }

    // 遍历每个库区任务
    for (const auto& kuqu_task : kuqu_tasks) {
        std::string kuqu_id = std::to_string(kuqu_task.kuqu_id);
        LOG_INFO_STREAM("Processing kuqu task: " << kuqu_task.id << " with kuqu_id: " << kuqu_id);

        // 尝试将库区绑定到每条拓扑边
        boost::graph_traits<Graph>::edge_iterator ei, ei_end;
        for (boost::tie(ei, ei_end) = boost::edges(graph_builder.getGraph().second); ei != ei_end; ++ei) {
            const EdgeData& edge_data = graph_builder.getGraph().second[*ei];

            // 检查这条拓扑边是否能穿过库区，同时获取切割点和切割索引
            auto [can_bind, cut_points, edge_cut_indices] = canKuquBindToTopologyEdgeWithCutPoints(kuqu_id, edge_data);
            if (can_bind) {
                LOG_INFO_STREAM("Kuqu " << kuqu_id << " can bind to topology edge: " << edge_data.id);

                // 使用计算好的切割点和索引进行切割，返回切割后的库区边
                std::vector<CutKuquEdge> cut_kuqu_edges = cutKuquEdgesWithIndices(kuqu_id, cut_points, edge_cut_indices);

                if (!cut_points.empty()) {
                    const CutPoint& last_cut_point = cut_points.back();
                    // std::string end_node_id = insertEndNodeForKuqu(kuqu_id, edge_data.id, last_cut_point);
                    // 在库区终点插入一个拓扑节点

                    // 创建绑定关系
                    KuquBinding binding;
                    binding.kuqu_id = kuqu_id;
                    binding.topology_edge_id = edge_data.id;
                    binding.cut_points = cut_points;
                    binding.end_node_id = end_node_id;

                    // 使用这次绑定特定的切割后库区边
                    binding.kuqu_edges = cut_kuqu_edges;

                    // 存储绑定关系（支持多个绑定）
                    kuqu_bindings[kuqu_id].push_back(binding);

                    LOG_INFO_STREAM("Successfully bound kuqu " << kuqu_id << " to topology edge " << edge_data.id
                                    << " with " << cut_points.size() << " cut points and end node " << end_node_id
                                    << " (total bindings for this kuqu: " << kuqu_bindings[kuqu_id].size() << ")");
                }
            }
        }
    }

    // 输出库区绑定总结信息
    logKuquBindingSummary();
}

// 清理方法
void KuquProcessor::clear() {
    kuqu_tasks.clear();
    normal_tasks.clear();
    kuqu_bindings.clear();
    kuqu_edges_map.clear();
}

// 判断库区是否能绑定到拓扑边，同时计算切割点和每条边的切割索引
std::tuple<bool, std::vector<KuquProcessor::CutPoint>, std::map<std::string, size_t>> KuquProcessor::canKuquBindToTopologyEdgeWithCutPoints(
    const std::string& kuqu_id, const EdgeData& topology_edge_data) {
    std::vector<CutPoint> cut_points;
    std::map<std::string, size_t> edge_cut_indices;

    // 首先找到对应的库区任务
    const cotek::task_t* kuqu_task = nullptr;
    for (const auto& task : kuqu_tasks) {
        if (std::to_string(task.kuqu_id) == kuqu_id) {
            kuqu_task = &task;
            break;
        }
    }

    if (!kuqu_task) {
        LOG_WARN_STREAM("Kuqu task not found for kuqu_id: " << kuqu_id);
        return {false, cut_points, edge_cut_indices};
    }

    // 检查拓扑边的任务是否在库区的collision列表中
    bool has_collision = false;
    for (const auto& collision : kuqu_task->collision) {
        if (collision.task_id == topology_edge_data.task_id) {
            has_collision = true;
            break;
        }
    }

    if (!has_collision) {
        LOG_DEBUG_STREAM("Topology edge task " << topology_edge_data.task_id
                        << " not in kuqu " << kuqu_id << " collision list");
        return {false, cut_points, edge_cut_indices};
    }

    // 获取库区边
    auto it = kuqu_edges_map.find(kuqu_id);
    if (it == kuqu_edges_map.end()) {
        return {false, cut_points, edge_cut_indices};
    }

    const auto& kuqu_edges = it->second;
    if (kuqu_edges.empty()) {
        return {false, cut_points, edge_cut_indices};
    }

    // 获取拓扑边对应的任务和边的点集
    std::vector<cotek::node_t> topology_points;
    for (const auto& task : normal_tasks) {
        if (task.id == topology_edge_data.task_id) {
            for (const auto& edge : task.edges) {
                if (edge.id == topology_edge_data.task_edge_id) {
                    size_t start_idx = topology_edge_data.start_index;
                    size_t end_idx = topology_edge_data.end_index;
                    for (size_t i = start_idx; i <= end_idx && i < edge.points.size(); ++i) {
                        topology_points.push_back(edge.points[i]);
                    }
                    break;
                }
            }
            break;
        }
    }

    if (topology_points.empty()) {
        LOG_WARN_STREAM("No topology points found for edge: " << topology_edge_data.id);
        return {false, cut_points, edge_cut_indices};
    }

    const double threshold = 0.5; // 0.5米阈值

    // 检查每条库区边，同时计算切割点
    for (const auto& kuqu_edge : kuqu_edges) {
        double min_distance = std::numeric_limits<double>::max();
        size_t best_topo_index = 0;
        size_t best_kuqu_index = 0;
        cotek::node_t best_cut_point;
        bool found_close_point = false;

        // 为每条库区边找到最接近的拓扑边点作为切割点
        for (size_t i = 0; i < topology_points.size(); ++i) {
            const auto& topo_point = topology_points[i];

            for (size_t j = 0; j < kuqu_edge.original_points.size(); ++j) {
                const auto& kuqu_point = kuqu_edge.original_points[j];
                double dist = std::sqrt(std::pow(kuqu_point.x - topo_point.x, 2) +
                                      std::pow(kuqu_point.y - topo_point.y, 2));
                if (dist < min_distance && dist < threshold) {
                    min_distance = dist;
                    best_topo_index = topology_edge_data.start_index + i;
                    best_kuqu_index = j;
                    best_cut_point = topo_point;
                    found_close_point = true;
                }
            }
        }

        if (!found_close_point) {
            LOG_INFO_STREAM("Kuqu edge " << kuqu_edge.edge_id << " has no close points to topology edge");
            return {false, cut_points, edge_cut_indices};
        }

        // 创建切割点
        CutPoint cut_point;
        cut_point.index = best_topo_index;
        cut_point.kuqu_id = kuqu_id;
        cut_point.topology_edge_id = topology_edge_data.id;
        cut_point.point = best_cut_point;
        cut_points.push_back(cut_point);

        // 存储这条边的切割索引到局部变量（不修改全局状态）
        edge_cut_indices[kuqu_edge.edge_id] = best_kuqu_index;
    }

    // 按索引排序切割点
    std::sort(cut_points.begin(), cut_points.end(),
              [](const CutPoint& a, const CutPoint& b) {
                  return a.index < b.index;
              });

    LOG_INFO_STREAM("Kuqu " << kuqu_id << " can bind to topology edge " << topology_edge_data.id
                    << " (task: " << topology_edge_data.task_id << ") with " << cut_points.size() << " cut points");
    return {true, cut_points, edge_cut_indices};
}

// 切割库区边 - 使用传入的切割索引，返回切割后的边副本
std::vector<KuquProcessor::CutKuquEdge> KuquProcessor::cutKuquEdgesWithIndices(
    const std::string& kuqu_id,
    const std::vector<CutPoint>& cut_points,
    const std::map<std::string, size_t>& edge_cut_indices) {

    std::vector<CutKuquEdge> result_edges;

    auto it = kuqu_edges_map.find(kuqu_id);
    if (it == kuqu_edges_map.end() || cut_points.empty()) {
        return result_edges;
    }

    const auto& kuqu_edges = it->second;

    for (const auto& kuqu_edge : kuqu_edges) {
        // 查找这条边的切割索引
        auto cut_it = edge_cut_indices.find(kuqu_edge.edge_id);
        if (cut_it == edge_cut_indices.end()) {
            // 如果没有找到切割索引，跳过这条边
            LOG_WARN_STREAM("No cut index found for kuqu edge: " << kuqu_edge.edge_id);
            continue;
        }

        size_t cut_index = cut_it->second;

        // 创建切割后的边副本
        CutKuquEdge cut_edge = kuqu_edge;
        cut_edge.cut_index = cut_index;
        cut_edge.cut_points.clear();

        if (kuqu_edge.is_forward) {
            // 正向边：保留切割点之前的部分
            for (size_t i = 0; i <= cut_index && i < kuqu_edge.original_points.size(); ++i) {
                cut_edge.cut_points.push_back(kuqu_edge.original_points[i]);
            }
        } else {
            // 反向边：保留切割点之后的部分
            for (size_t i = cut_index; i < kuqu_edge.original_points.size(); ++i) {
                cut_edge.cut_points.push_back(kuqu_edge.original_points[i]);
            }
        }

        result_edges.push_back(cut_edge);

        LOG_INFO_STREAM("Cut kuqu edge " << kuqu_edge.edge_id << " at index " << cut_index
                        << ", forward: " << kuqu_edge.is_forward
                        << ", result points: " << cut_edge.cut_points.size());
    }

    return result_edges;
}

// 为库区插入终点节点
std::string KuquProcessor::insertEndNodeForKuqu(const std::string& kuqu_id, const std::string& topology_edge_id,
                                               const CutPoint& last_cut_point) {
    // 生成新的节点ID
    std::string new_node_id = "kuqu_end_" + kuqu_id + "_" + generateUUID();

    // 创建新的拓扑节点
    VertexData new_vertex_data;
    new_vertex_data.id = new_node_id;
    new_vertex_data.x = last_cut_point.point.x;
    new_vertex_data.y = last_cut_point.point.y;
    new_vertex_data.vertex_type = 1; // 任务点
    new_vertex_data.include_points.push_back(last_cut_point.point);

    // 添加到图中
    Vertex new_vertex = boost::add_vertex(new_vertex_data, topology_graph);

    LOG_INFO_STREAM("Inserted end node " << new_node_id << " for kuqu " << kuqu_id
                    << " at position (" << new_vertex_data.x << ", " << new_vertex_data.y << ")");

    return new_node_id;
}
