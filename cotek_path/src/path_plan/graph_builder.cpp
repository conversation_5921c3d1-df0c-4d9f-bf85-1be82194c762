#include <boost/graph/graphviz.hpp>
#include <boost/uuid/uuid.hpp>
#include <boost/uuid/uuid_generators.hpp>
#include <boost/uuid/uuid_io.hpp>
#include "path_plan/graph_builder.hpp"
#include <vector>

//namespace cotek {
//namespace plan {

// 计算两点之间的距离
inline double distance(const cotek::node_t& p1, const cotek::node_t& p2) {
    return std::sqrt(std::pow(p2.x - p1.x, 2) + std::pow(p2.y - p1.y, 2));
}

// 判断点的角度是否在给定范围内
bool isAngleInRange(double angle, double referenceAngle, double range) {
    // 将角度归一化到 [0, 360)
    angle = std::fmod(angle, 360.0);
    referenceAngle = std::fmod(referenceAngle, 360.0);

    // 计算角度之间的差值，考虑循环性质
    double diff = std::min(std::abs(angle - referenceAngle), 360.0 - std::abs(angle - referenceAngle));

    // 如果差值小于等于范围，则角度在范围内
    return diff <= range;
}

// 判断数组中是否存在满足条件的点
bool hasPointInRadiusAndAngleRange(const cotek::node_t& center, const std::vector<std::vector<cotek::node_t>>& paths, double radius, double angleRange) {
    for (const auto & path : paths){
        for (const cotek::node_t& p : path) {
            // 计算当前点与中心点的距离
            double dist = distance(center, p);

            // 如果距离小于等于半径，并且角度在给定范围内，则返回true
            if (dist <= radius && isAngleInRange(p.angle, center.angle, angleRange)) {
                return true;
            }
        }
    }
    return false;
}

// 生成唯一ID的函数
std::string generateUUID() {
    boost::uuids::uuid uuid = boost::uuids::random_generator()();
    return boost::uuids::to_string(uuid);
}

// 计算两点之间的角度（弧度）
inline double angleBetweenPoints(const cotek::node_t& p1, const cotek::node_t& p2) {
    return atan2(p2.y - p1.y, p2.x - p1.x)* (180.0 / M_PI);
}

// 计算曲线指定范围内的起始切线角和结尾切线角（以角度表示）
std::pair<double, double> calculateTangentAngles(const std::vector<cotek::node_t>& curve, size_t start, size_t end) {
    if (curve.size() < 2 || start >= curve.size() || end >= curve.size() || start > end) {
        throw std::invalid_argument("Invalid start or end position.");
    }

    // 计算起始切线角
    double startAngle = angleBetweenPoints(curve[start], curve[start + 1]);

    // 计算结尾切线角
    double endAngle = angleBetweenPoints(curve[end], curve[end-1]);

    return {startAngle, endAngle};
}

// 计算指定起始位置和结束位置的曲线长度, 顺带判断这条边的move type， 0前进，1倒车，2都可以
std::pair<double, int> getCurveLengthAndMoveType(const std::vector<cotek::node_t>& points, size_t start, size_t end) {
    // 确保起始位置和结束位置有效
    if (start >= points.size() || end >= points.size() || start > end) {
        throw std::invalid_argument("Invalid start or end position.");
    }

    int moveType;
    double length = 0.0;

    bool hasPositive = false, hasNegative = false;
    // 遍历指定范围内的点
    for (size_t i = start; i < end; ++i) {
        length += distance(points[i], points[i + 1]);
        if(hasPositive && hasNegative) continue;
        if (points[i].type < 0){
            hasNegative = true;
        }else{
            hasPositive = true;
        }

    }
    if (hasPositive && hasNegative) {
        moveType = 2;
    } else if (hasNegative) {
        moveType = 1;
    } else {
        moveType = 0;
    }
    return std::make_pair(length, moveType);
}


//在当前采集点创建一个拓扑点，分割拓扑边，并返回下一条拓扑边
std::pair<Edge, EdgeData> GraphBuilder::constructVertexAndDividedEdge(const std::vector<cotek::node_t>& path, size_t current_index, Edge& current_edge,
                                                 const EdgeData& current_data) {
    VertexData data = VertexData{path[current_index].id,
                                 path[current_index].x,
                                 path[current_index].y,
                                  std::vector<cotek::node_t>{path[current_index]},
                                  2  // 0/1/2 采集点/任务点/虚拟点
    };
    Vertex middleVertex = addMergedVertex(data).first;


//    Vertex middleVertex = boost::add_vertex(g);
//    g[middleVertex].id = current_point.id;
//    g[middleVertex].x = current_point.x;
//    g[middleVertex].y = current_point.y;
//    g[middleVertex].vertex_type = 2; // 0/1/2 采集点/任务点/虚拟点

    // 获取当前边的 source 和 target descriptor
    Vertex left = boost::source(current_edge, g);
    Vertex right = boost::target(current_edge, g);


    // 如果中间节点 与左或者右 是同一个，则不需要划分边
    if (left == middleVertex || middleVertex == right){
        return std::make_pair(current_edge, current_data);
    }

    // 直接把EdgeData 传进来
    EdgeData edgeData1;
    edgeData1.id = generateUUID();
    edgeData1.weight = getCurveLengthAndMoveType(path, current_data.start_index, current_index).first;
    edgeData1.move_type = current_data.move_type;
    std::tie(edgeData1.source_yaw, edgeData1.target_yaw) = calculateTangentAngles(path, current_data.start_index, current_index);
    //edgeData1.source_id = path[current_data.start_index].id;
    edgeData1.target_id = path[current_index].id;
    edgeData1.start_index = current_data.start_index;
    edgeData1.end_index = current_index;
    edgeData1.task_id = current_data.task_id;
    edgeData1.task_edge_id = current_data.task_edge_id;


    EdgeData edgeData2;
    edgeData2.id = generateUUID();
    edgeData2.weight = getCurveLengthAndMoveType(path, current_index, current_data.end_index).first;
    edgeData2.move_type = current_data.move_type;
    std::tie(edgeData2.source_yaw, edgeData2.target_yaw) = calculateTangentAngles(path, current_index, current_data.end_index);
    //edgeData2.source_id = path[current_index].id;
    edgeData2.target_id = path[current_data.end_index].id;
    edgeData2.start_index = current_index;
    edgeData2.end_index = current_data.end_index;
    edgeData2.task_id = current_data.task_id;
    edgeData2.task_edge_id = current_data.task_edge_id;

    // 删除边再添加
    g.remove_edge(current_edge);

    //Edge e1 = boost::add_edge(left, middleVertex, g).first;
    Edge e1 = addEdge(left, middleVertex, edgeData1);

    //Edge e2 = boost::add_edge(middleVertex, right, g).first;
    Edge e2 = addEdge(middleVertex, right, edgeData2);

    return std::make_pair(e2, edgeData2);
}




void GraphBuilder::constructAPathTopology(const cotek::edge_t &edge,
                                          const std::vector<std::vector<cotek::node_t>> &otherPaths,
                                          const std::vector<size_t> &traffics, const std::string& current_task_id) {
    std::vector<cotek::node_t> current_path = edge.points;
    // 首先在当前path的头尾创建站点，并连接为一条边
    VertexData data1 = VertexData{current_path[0].id,
                                  current_path[0].x,
                                  current_path[0].y,
                                  std::vector<cotek::node_t>{current_path[0]},
                                  1
    };
    Vertex v1;
    VertexData v1Data;
    std::tie(v1, v1Data) = addMergedVertex(data1);
    point2TopologyMap.emplace(edge.start_point.id, v1Data.id);

    VertexData data2 = VertexData{current_path[current_path.size()-1].id,
                                  current_path[current_path.size()-1].x,
                                  current_path[current_path.size()-1].y,
                                  std::vector<cotek::node_t>{current_path[current_path.size()-1]},
                                  1  // 0/1/2 采集点/任务点/虚拟点
    };
    Vertex v2;
    VertexData v2Data;
    std::tie(v2, v2Data) = addMergedVertex(data2);
    point2TopologyMap.emplace(edge.end_point.id, v2Data.id);

    EdgeData edgeData;
    edgeData.id = generateUUID();
    std::tie(edgeData.weight, edgeData.move_type) = getCurveLengthAndMoveType(current_path, 0, current_path.size()-1);
    if(edge.direction == 1){
        edgeData.move_type = 0;
    }else if(edge.direction == -1){
        edgeData.move_type = 1;
    }else{
        edgeData.move_type = 2;
    }
    std::tie(edgeData.source_yaw, edgeData.target_yaw) = calculateTangentAngles(current_path, 0, current_path.size()-1);
    edgeData.source_id = current_path[0].id;
    edgeData.target_id = current_path[current_path.size()-1].id;
    edgeData.start_index = 0;
    edgeData.end_index = current_path.size()-1;  // 设置为一个可索引的值
    edgeData.task_id = current_task_id;
    edgeData.task_edge_id = edge.id;
    edgeData.ori_id = edge.id;
    edgeData.velocity_level = edge.velocity_level;
    edgeData.avoid_level = edge.avoid_level;


    Edge current_edge = addEdge(v1, v2, edgeData); // 当前边为一整条边

    // 如果不存在交管，说明这条边不会和其他边交叉，直接返回
    if(otherPaths.empty()){
        return;
    }

    bool is_overlay = false; // 当前状态未重合

    // todo: 只遍历交管区域里面的点
    //std::vector<size_t> traffics{0,current_path.size()-1};
    for (size_t i = 0; i < traffics.size(); i += 2) {
        if (i + 1 < traffics.size()) { // 确保有起始和终止位置
            // 判断每一个点是否与其他点在一一定范围内重复
            for (size_t j=traffics[i]; j < traffics[i+1]; ++j){
                if (!is_overlay && hasPointInRadiusAndAngleRange(current_path[j], otherPaths,
                                                                 point_overlay_r, point_overlay_angle)){
                    // 如果重复且当前状态未重合，则在当前位置添加一个拓扑节点，并将当前边分为两条边
                    std::tie(current_edge, edgeData) = constructVertexAndDividedEdge(current_path, j, current_edge, edgeData);
                    is_overlay = true;

                } else if(is_overlay && !hasPointInRadiusAndAngleRange(current_path[j], otherPaths,
                                                                       point_overlay_r, point_overlay_angle)){
                    // 如果未重复且当前状态重合，则在当前位置添加一个拓扑节点，并将当前边分为两条边
                    std::tie(current_edge, edgeData) = constructVertexAndDividedEdge(current_path, j, current_edge, edgeData);
                    is_overlay = false;
                }
            }
        }
    }

}

// 合并索引的函数
std::vector<size_t> mergeIntervals(const std::vector<size_t>& input) {
    if(input.empty()){
        return input;
    }
    if (input.size() % 2 != 0) {
        throw std::invalid_argument("Input array must contain an even number of elements.");
    }

    std::vector<std::pair<size_t, size_t>> intervals;

    // Creating intervals
    for (size_t i = 0; i < input.size(); i += 2) {
        intervals.emplace_back(input[i], input[i + 1]);
    }

    // Sorting intervals
    std::sort(intervals.begin(), intervals.end());

    // Merging intervals
    std::vector<size_t> merged;
    merged.push_back(intervals[0].first);
    merged.push_back(intervals[0].second);

    for (size_t i = 1; i < intervals.size(); ++i) {
        size_t lastEnd = merged.back();
        size_t currentStart = intervals[i].first;
        size_t currentEnd = intervals[i].second;

        if (currentStart <= lastEnd) {
            merged.back() = std::max(lastEnd, currentEnd);
        } else {
            merged.push_back(currentStart);
            merged.push_back(currentEnd);
        }
    }

    return merged;
}


// 找到边 id 的所有采集点
std::vector<cotek::node_t> findEdgeNodesFromTasks(const std::string& id, const std::vector<cotek::task_t>& tasks){
    for (const auto& task : tasks){
        for (const auto& edge : task.edges){
            if (edge.id == id) {
                return edge.points;
            }
        }
    }

    // 如果没有找到，返回一个空的std::vector<node_t>
    return {};

}



// 获取一条边上交管区域的其他边的节点集合
std::vector<std::vector<cotek::node_t>>  GraphBuilder::edgeTrafficOtherPoints(const std::vector<cotek::task_t>& tasks, const std::string& currentEdgeId, const std::vector<std::pair<int, EdgePart>>& currentTraffics){
    std::vector<std::vector<cotek::node_t>> res;

    for (const auto& traffic : currentTraffics){
        TrafficArea temp = trafficIdMap.at(traffic.first);
        //找到交管区域中另一条路径的索引
        if (temp.task1 == currentEdgeId){ // currentEdgeId 这里是taskid
            // 则 line2 是另外一条路径
            // 将交管另一个任务的边放进来
            for (const auto& edge : temp.edges2){
                auto anotherPath = findEdgeNodesFromTasks(edge.edgeId, tasks);
                //if(anotherPath.empty()) continue;
                std::vector<cotek::node_t> subPath(anotherPath.begin()+edge.startIndex, anotherPath.begin()+edge.endIndex);
                res.emplace_back(std::move(subPath));
            }
        }else{
            // line1 是另外一条路径
            for (const auto& edge : temp.edges1){
                auto anotherPath = findEdgeNodesFromTasks(edge.edgeId, tasks);
                std::vector<cotek::node_t> subPath(anotherPath.begin()+edge.startIndex, anotherPath.begin()+edge.endIndex);
                res.emplace_back(std::move(subPath));
            }
        }
    }
    return res;

}

void GraphBuilder::constructTopologicalGraph(const std::vector<cotek::task_t>& tasks){

    for (size_t i =0; i < tasks.size(); ++i){
        // 考虑当前 task 的一段路线，也就是一条边 edge，对每条边构造拓扑地图
        for(const auto& edge : tasks[i].edges){
            // 获取其他任务上与这条边有可能相交的点
            std::vector<std::vector<cotek::node_t>> otherPaths;
            // 获取当前边的交管数组，方便对其进行合并
            std::vector<size_t > exactTraffics;

            if(edgeTrafficsMap.find(edge.id) != edgeTrafficsMap.end()){
                otherPaths = edgeTrafficOtherPoints(tasks, tasks[i].id, edgeTrafficsMap.at(edge.id));

                for (const auto& t : edgeTrafficsMap.at(edge.id)){
                    exactTraffics.emplace_back(t.second.startIndex);
                    exactTraffics.emplace_back(t.second.endIndex);
                }
            }

            constructAPathTopology(edge, otherPaths, mergeIntervals(exactTraffics), tasks[i].id);

        } 
    }

}

// 获取交管范围内的部分边（包括id以及索引）
std::vector<GraphBuilder::EdgePart> GraphBuilder::findEdgesInRange(const std::vector<cotek::edge_t> &edges, int start, int end) {
    std::vector<EdgePart> result;
    int currentIndex = 0;

    for (auto& e : edges) {
        int edgeStart = currentIndex;
        int edgeEnd = edgeStart + e.points.size() - 1;

        if (end < edgeStart) {
            // 如果给定范围的结束索引还没有到当前边的开始，搜索结束
            break;
        }

        if (start <= edgeEnd) {
            // 计算边的ID以及范围内的索引，添加到结果中
            EdgePart part;
            part.edgeId = e.id;
            part.startIndex = std::max(start - edgeStart, 0);
            part.endIndex = std::min(end - edgeStart, static_cast<int>(e.points.size()) - 1);

            result.push_back(part);
        }

        currentIndex += e.points.size();
    }

    return result;
}

// 处理交管信息，生成两个map，便于索引
void GraphBuilder::handleTraffics(const std::vector<cotek::task_t>& tasks){
    for (const auto& task : tasks){

        for (const auto& traffic : task.collision){ //task.traffic
            // todo:将交管分割，交管在那几条边上，以及索引
            // 找到交管上的 部分边
            auto edgeParts = findEdgesInRange(task.edges, traffic.start_index, traffic.end_index);

            for (const auto& edgePart : edgeParts){
                // 一个是记录一条边的所有交管
                this->edgeTrafficsMap[edgePart.edgeId].emplace_back(traffic.id, edgePart);
            }

            // 一个是根据 交管id进行 索引
            auto it = this->trafficIdMap.find(traffic.id);
            if(it == this->trafficIdMap.end()){
                trafficIdMap[traffic.id].task1 = task.id;
                trafficIdMap[traffic.id].edges1 = edgeParts;
            }else{
                trafficIdMap[traffic.id].task2 = task.id;
                trafficIdMap[traffic.id].edges2 = edgeParts;
            }

        }
    }

}

size_t convertToSizeT(const std::string& str) {
    if (str.size() < 4) {
        // 如果字符串长度小于4，则无法转换为size_t
        // 在这里你可以选择抛出异常或者返回默认值，这里返回0作为示例
        return 0;
    }

    std::string trimmed = str.substr(4); // 去除前四个字符
    return std::stoul(trimmed); // 转换为size_t类型
}

auto GraphBuilder::findVertexDescriptor(const VertexData& data){
    // 定义顶点属性映射
    typedef boost::property_map<Graph, std::string VertexData::*>::type IdMap;
    IdMap idMap = boost::get(&VertexData::id, g);

    // 查找节点描述符
    Vertex vd;
    bool found = false;
    boost::graph_traits<Graph>::vertex_iterator vi, vi_end;

    for (boost::tie(vi, vi_end) = boost::vertices(g); vi != vi_end; ++vi) {
        if (idMap[*vi] == data.id) {
            vd = *vi;
            found = true;
            break;
        }
    }

    // 输出查找结果
    if (found) {
        return std::make_pair(found, vd);
    } else {
        LOG_ERROR_STREAM( "can not find vertex descriptor" );
        return std::make_pair(found, vd);
    }
}

std::pair<Vertex, VertexData> GraphBuilder::addMergedVertex(const VertexData& data){
    // 首先判断是否已经存在节点
    std::vector<VertexData> res;

//        rtree.query(bgi::satisfies([&data](const VertexData& d){
//            return bg::distance(data, d) <= vertex_r;  //找到符合条件的集合
//        }), std::back_inserter(res));

    // 这里只考虑坐标位置不考虑角度，进行拓扑节点合并
    rtree.query(bgi::nearest(data, 1), std::back_inserter(res));


    // 如果不存在节点，或者找到最近的节点位置很远，则添加
    if(res.empty() || bg::distance(data, res[0]) > vertex_r){
        // 添加到g
        Vertex v1 = boost::add_vertex(g);
        g[v1].id = data.id;
        g[v1].x = data.x;
        g[v1].y = data.y;
        g[v1].vertex_type = data.vertex_type; // 0/1/2 采集点/任务点/虚拟点

        // 添加到boost graph和 rtree
        rtree.insert(data);

        return std::make_pair(v1, g[v1]);
    }
    // 如果存在，直接返回已有的 Vertex descriptor
    auto result = findVertexDescriptor(res[0]);
    // 返回已有的 Vertex descriptor 以及已存在拓扑节点的id
    return std::make_pair(result.second, res[0]);
    //return result.second;

}

Edge GraphBuilder::addEdge(const Vertex& v1, const Vertex& v2, const EdgeData& data){
    Edge e = boost::add_edge(v1, v2, g).first;
    // todo: EdgeData needs to  be set, weight, task_id...
    g[e].id = data.id;
    g[e].weight = data.weight;
    g[e].move_type = data.move_type;
    g[e].start_index = data.start_index;
    g[e].end_index = data.end_index;
    g[e].source_yaw = data.source_yaw;
    g[e].target_yaw = data.target_yaw;
    g[e].task_id = data.task_id;
    g[e].task_edge_id = data.task_edge_id;
    g[e].ori_id = data.ori_id;
    g[e].source_id = g[v1].id;
    g[e].target_id = g[v2].id;
    g[e].ori_target_id = data.target_id;
    g[e].velocity_level = data.velocity_level;
    g[e].avoid_level = data.avoid_level;
    
//    g[e].source_id = data.source_id;
//    g[e].target_id = data.target_id;

    return e;
}

//     dot -Tpng test.dot -o graph.png
void GraphBuilder::exportDotFile(const std::string& filename) {

    std::ofstream dotfile;
    dotfile.open(filename);
    if (!dotfile) {
        // 文件打开失败的处理
        LOG_ERROR_STREAM( "Error opening file: " << filename );
        return;
    }

    boost::write_graphviz(dotfile, g,
            // 顶点属性的写入函数
                          [&](std::ostream& out, const Graph::vertex_descriptor& v) {
                              out << "[node_id=\"" << g[v].id <<
                            //   out << "[label=\"" << g[v].id <<
                              "\", label=\"" << g[v].id <<
                              "\", x=\"" << g[v].x <<
                              "\", y=\"" << g[v].y <<
                              "\",vertex_type=\"" << g[v].vertex_type <<
                              "\"]";
                          },
            // 边属性的写入函数
                          [&](std::ostream& out, const Graph::edge_descriptor& e) {
                              out << "[weight=\"" << g[e].weight <<
                              "\", source_id=\"" << g[e].source_id <<
                              "\", target_id=\"" << g[e].target_id <<
                              "\", id=\"" << g[e].id <<
                              "\", task_id=\"" << g[e].task_id <<
                              "\", move_type=\"" << g[e].move_type <<
                              "\", source_yaw=\"" << g[e].source_yaw <<
                              "\", target_yaw=\"" << g[e].target_yaw <<
                              "\", task_edge_id=\"" << g[e].task_edge_id <<
                              "\", start_index=\"" << g[e].start_index <<
                              "\", end_index=\"" << g[e].end_index <<
                              "\", ori_target_id=\"" << g[e].ori_target_id <<
                              "\", velocity_level=\"" << g[e].velocity_level <<
                              "\", avoid_level=\"" << g[e].avoid_level <<
                              "\"]";
                          });

    // 检查写入是否成功
    if (dotfile.fail()) {
        LOG_ERROR_STREAM( "Error writing to file: " << filename );
    } else {
        LOG_INFO_STREAM( "File exported successfully: " << filename );
    }

    dotfile.close();

}

Graph GraphBuilder::loadMergedGraph(const std::string& fileName) {
    Graph graph;

    // 准备动态属性，将每个属性关联到图形的顶点和边
    boost::dynamic_properties dp;
    dp.property("node_id", boost::get(&VertexData::id, graph));
    dp.property("label", boost::get(&VertexData::id, graph));
    dp.property("x", boost::get(&VertexData::x, graph));
    dp.property("y", boost::get(&VertexData::y, graph));
    dp.property("vertex_type", boost::get(&VertexData::vertex_type, graph));

    dp.property("weight", boost::get(&EdgeData::weight, graph));
    dp.property("source_id", boost::get(&EdgeData::source_id, graph));
    dp.property("target_id", boost::get(&EdgeData::target_id, graph));
    dp.property("id", boost::get(&EdgeData::id, graph));
    dp.property("task_id", boost::get(&EdgeData::task_id, graph));
    dp.property("move_type", boost::get(&EdgeData::move_type, graph));
    dp.property("source_yaw", boost::get(&EdgeData::source_yaw, graph));
    dp.property("target_yaw", boost::get(&EdgeData::target_yaw, graph));
    dp.property("task_edge_id", boost::get(&EdgeData::task_edge_id, graph));
    dp.property("start_index", boost::get(&EdgeData::start_index, graph));
    dp.property("end_index", boost::get(&EdgeData::end_index, graph));
    dp.property("ori_target_id", boost::get(&EdgeData::ori_target_id, graph));
    dp.property("velocity_level", boost::get(&EdgeData::velocity_level, graph));
    dp.property("avoid_level", boost::get(&EdgeData::avoid_level, graph));

    // 从DOT文件读取图形
    std::ifstream dot_file(fileName);
    if (!dot_file.is_open()) {
        LOG_ERROR_STREAM( "Error opening file" );

    }

    // 读取文件为 Graph
    bool status = boost::read_graphviz(dot_file, graph, dp, "node_id");
    // bool status = boost::read_graphviz(dot_file, graph, dp, "label");
    if (!status) {
        LOG_ERROR_STREAM( "Error reading DOT file" );
    }

    return graph;
}


// 在指定task的指定index处插入拓扑节点，切割拓扑边
GraphBuilder::InsertNodeResult GraphBuilder::insertTopologyNodeAtIndex(const std::string& task_id,
                                                                        size_t index,
                                                                        const std::vector<cotek::task_t>& tasks) {
    InsertNodeResult result;
    result.success = false;

    // 1. 查找指定的task
    const cotek::task_t* target_task = nullptr;
    for (const auto& task : tasks) {
        if (task.id == task_id) {
            target_task = &task;
            break;
        }
    }

    if (!target_task) {
        LOG_ERROR_STREAM( "Task with id '" + task_id + "' not found" );
        return result;
    }

    // 2. 找到包含指定index的edge以及在该edge中的相对位置
    const cotek::edge_t* target_edge = nullptr;

    for (const auto& edge : target_task->edges) {

        if (index >= edge.start_index && index <= edge.end_index) {
            target_edge = &edge;
            break;
        }
    }

    if (!target_edge) {
        LOG_ERROR_STREAM( "Index " + std::to_string(index) + " is out of range for task '" + task_id + "'" );
        return result;
    }

    // 4. 在图中查找对应的拓扑边
    Edge target_topology_edge;
    EdgeData target_edge_data;
    bool edge_found = false;

    boost::graph_traits<Graph>::edge_iterator ei, ei_end;
    for (boost::tie(ei, ei_end) = boost::edges(g); ei != ei_end; ++ei) {
        if (g[*ei].task_id == task_id && g[*ei].task_edge_id == target_edge->id) {
            // 检查index是否在这条拓扑边的范围内
            if (index >= g[*ei].start_index && index <= g[*ei].end_index) {
                target_topology_edge = *ei;
                target_edge_data = g[*ei];
                edge_found = true;
                break;
            }
        }
    }

    if (!edge_found) {
        LOG_ERROR_STREAM( "Corresponding topology edge not found for task '" + task_id + "' at index " + std::to_string(index) );
        return result;
    }

    // 5. 创建新的拓扑节点
    size_t relative_index = index - target_edge->start_index;
    // 检查index是否在有效范围内（不能是边的起点或终点）
    if(relative_index <= 0 || relative_index >= target_edge->points.size()) {
        LOG_ERROR_STREAM( "Cannot insert node at edge start or end point (index: " + std::to_string(index) + ")" );
        return result;
    }

    const cotek::node_t& insert_point = target_edge->points[relative_index];
    VertexData new_vertex_data;
    new_vertex_data.id = generateUUID();
    new_vertex_data.x = insert_point.x;
    new_vertex_data.y = insert_point.y;
    new_vertex_data.include_points = std::vector<cotek::node_t>{insert_point};
    new_vertex_data.vertex_type = 2; // 虚拟点

    // 6. 使用现有的方法切割边
    std::pair<Edge, EdgeData> division_result = constructVertexAndDividedEdge(
        target_edge->points,
        relative_index,
        target_topology_edge,
        target_edge_data
    );

    // 7. 填充返回结果
    result.success = true;
    result.new_node_id = new_vertex_data.id;
    result.new_node_data = new_vertex_data;
    result.second_edge_id = division_result.second.id;

    // 查找第一条边的ID（被切割后的原边）
    boost::graph_traits<Graph>::edge_iterator ei2, ei2_end;
    for (boost::tie(ei2, ei2_end) = boost::edges(g); ei2 != ei2_end; ++ei2) {
        if (g[*ei2].task_id == task_id &&
            g[*ei2].task_edge_id == target_edge->id &&
            g[*ei2].end_index == index &&
            g[*ei2].id != division_result.second.id) {
            result.first_edge_id = g[*ei2].id;
            break;
        }
    }

    return result;
}

//} // plan
//} // cotek






