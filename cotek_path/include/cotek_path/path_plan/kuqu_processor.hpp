//
// Created by o<PERSON> on 24-12-29.
//
#ifndef KUQU_PROCESSOR_H
#define KUQU_PROCESSOR_H

#include <string>
#include <vector>
#include <map>
#include <utility>
#include <boost/graph/adjacency_list.hpp>
#include "graph_builder.hpp"
#include "var/var.h"
#include "cotek_common/log_porting.h"

class KuquProcessor {
public:
    // 库区边信息
    struct CutKuquEdge {
        std::string edge_id;           // 库区边ID
        std::string task_id;           // 所属任务ID
        bool is_forward;               // 是否为正向边
        std::vector<cotek::node_t> original_points;  // 原始点集
        std::vector<cotek::node_t> cut_points;       // 切割后的点集
        size_t cut_index;              // 切割点在原始边中的索引
    };

    // 切割点信息
    struct CutPoint {
        size_t index;                  // 在拓扑边中的索引位置
        std::string kuqu_id;           // 所属库区ID
        std::string topology_edge_id;  // 所在拓扑边ID
        cotek::node_t point;           // 切割点坐标
    };

    // 库区绑定信息
    struct KuquBinding {
        std::string kuqu_id;           // 库区ID
        std::string topology_edge_id;  // 绑定的拓扑边ID
        size_t start_index;            // 库区起点 index
        size_t end_index;              // 库区终点 index
        std::string end_node_id;       // 库区规划终点 拓扑节点
        std::vector<CutPoint> cut_points;  // 该拓扑边上的切割点列表
        std::vector<CutKuquEdge> kuqu_edges;  // 库区中的边列表
        
    };

private:
    // 库区任务相关数据
    std::vector<cotek::task_t> kuqu_tasks;                    // 库区任务列表
    std::vector<cotek::task_t> normal_tasks;                  // 正常任务列表
    std::map<std::string, std::vector<KuquBinding>> kuqu_bindings;  // 库区绑定关系 key: kuqu_id, value: 多个绑定
    std::map<std::string, std::vector<CutKuquEdge>> kuqu_edges_map;    // 库区边映射 key: kuqu_id

    // 引用GraphBuilder的图和相关数据
    GraphBuilder& graph_builder;
    std::vector<cotek::task_t>& all_tasks;

public:
    // 构造函数
    KuquProcessor(GraphBuilder& graph_builder, std::vector<cotek::task_t>& all_tasks);

    // 主要处理方法
    void processKuquTasks(const std::vector<cotek::task_t>& all_tasks);
    
    // 分离库区任务和正常任务
    void separateKuquTasks(const std::vector<cotek::task_t>& tasks);
    
    // 处理所有库区任务的绑定
    void bindAllKuquToTopology();
    
    // 判断库区是否能绑定到拓扑边，同时计算切割点和每条边的切割索引
    std::tuple<bool, std::vector<CutPoint>, std::map<std::string, size_t>> canKuquBindToTopologyEdgeWithCutPoints(
        const std::string& kuqu_id, const EdgeData& topology_edge_data);
    
    // 切割库区边 - 传入每条边的切割索引
    std::vector<CutKuquEdge> cutKuquEdgesWithIndices(const std::string& kuqu_id,
                                                     const std::vector<CutPoint>& cut_points,
                                                     const std::map<std::string, size_t>& edge_cut_indices);
    
    // 为库区插入终点节点
    std::string insertEndNodeForKuqu(const std::string& kuqu_id, const std::string& topology_edge_id, 
                                     const CutPoint& last_cut_point);
    
    // 输出库区绑定总结信息
    void logKuquBindingSummary();

    // 获取器方法
    const std::vector<cotek::task_t>& getKuquTasks() const { return kuqu_tasks; }
    const std::vector<cotek::task_t>& getNormalTasks() const { return normal_tasks; }
    const std::map<std::string, std::vector<KuquBinding>>& getKuquBindings() const { return kuqu_bindings; }
    const std::map<std::string, std::vector<CutKuquEdge>>& getKuquEdgesMap() const { return kuqu_edges_map; }

    // 清理方法
    void clear();
};

#endif // KUQU_PROCESSOR_H
